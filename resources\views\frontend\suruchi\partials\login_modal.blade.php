<div class="modal" id="login_modal" data-animation="slideInUp">
    <div class="modal-dialog quickview__main--wrapper">
        <h3> {{ translate('Login') }} </h3>
        <header class="modal-header quickview__header">
            <button class="close-modal quickview__close--btn" aria-label="close modal" data-close="">✕ </button>
        </header>
        <div class="c-preloader text-center p-3">
            <i class="las la-spinner la-spin la-3x"></i>
        </div>
        <div class="quickview__inner">
            <div class="p-3">
                <form class="form-default account__login--inner" role="form" action="{{ route('cart.login.submit') }}" method="POST">
                    @csrf

                    <!-- Conditional Email/Phone Login -->
                    @if (get_setting('otp_system'))
                        <div class="form-group phone-form-group mb-1">
                            <input type="tel" id="phone-code" class="account__login--input" name="phone" placeholder="{{  translate('Phone') }}" value="{{ old('phone') }}" autocomplete="off">
                        </div>
                        <input type="hidden" name="country_code" value="+91">
                        <div class="form-group email-form-group mb-1 d-none">
                            <input type="email" class="account__login--input" name="email" placeholder="{{ translate('Email') }}" value="{{ old('email') }}" autocomplete="off">
                            @error('email')
                            <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>
                        <button type="button" class="btn btn-link p-0 text-primary fs-12" onclick="toggleEmailPhone(this)">
                            <i>*{{ translate('Use Email Instead') }}</i>
                        </button>
                    @else
                        <div class="form-group">
                            <input type="email" class="account__login--input" placeholder="{{  translate('Email') }}" name="email" value="{{ old('email') }}" autocomplete="off">
                            @error('email')
                            <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>
                    @endif

                    <!-- Phone OTP Field (for phone login) -->
                    @if (get_setting('otp_system'))
                        <div id="phone-otp-input-group" class="form-group d-none">
                            <input type="text" id="phone_otp" name="phone_otp" class="account__login--input" placeholder="{{ translate('Enter Phone OTP') }}" autocomplete="off">
                        </div>
                    @endif

                    <!-- Email OTP Field (for email OTP login) -->
                    @if (get_setting('ask_email_otp_to_login'))
                        <div id="email-otp-input-group" class="form-group d-none">
                            <input type="text" id="email_otp" name="email_otp" class="account__login--input" placeholder="{{ translate('Enter Email OTP') }}" autocomplete="off">
                        </div>
                    @endif

                    <!-- Password Field (for regular login) -->
                    @if (!get_setting('ask_email_otp_to_login'))
                        <div id="password-input-group" class="form-group">
                            <label for="password" class="fs-12 fw-700 text-soft-dark"></label>
                            <div class="position-relative">
                                <input type="password" id="password" class="account__login--input" name="password" placeholder="{{ translate('Password')}}">
                                <i class="password-toggle las la-eye"></i>
                            </div>
                        </div>
                    @endif

                    <div class="account__login--remember__forgot mb-15 d-flex justify-content-between align-items-center">
                        <div class="account__login--remember position__relative">
                            <input class="checkout__checkbox--input" id="check1" type="checkbox" {{ old('remember') ? 'checked' : '' }}>
                            <span class="checkout__checkbox--checkmark"></span>
                            <label class="checkout__checkbox--label login__remember--label" for="check1">
                                Remember me</label>
                        </div>
                        <a href="{{ route('password.request') }}" class="account__login--forgot">Forgot Your Password?</a>
                    </div>

                    <!-- Dynamic Login Button -->
                    <button id="login-btn" class="account__login--btn primary__btn" type="button" onclick="handleLogin()">
                        {{ translate('Login') }}
                    </button>
                </form>

                <!-- Social Login -->
                @if(get_setting('google_login') || get_setting('facebook_login') || get_setting('twitter_login') || get_setting('apple_login'))
                    <div class="account__login--divide"><span class="account__login--divide__text">OR</span></div>
                    <div class="account__social d-flex justify-content-center mb-15">
                        @if(get_setting('facebook_login'))
                            <a href="{{ route('social.login', ['provider' => 'facebook']) }}" class="account__social--link facebook">Facebook</a>
                        @endif
                        @if(get_setting('google_login'))
                            <a href="{{ route('social.login', ['provider' => 'google']) }}" class="account__social--link google">Google</a>
                        @endif
                        @if(get_setting('twitter_login'))
                            <a href="{{ route('social.login', ['provider' => 'twitter']) }}" class="account__social--link twitter">Twitter</a>
                        @endif
                        @if(get_setting('apple_login'))
                            <a href="{{ route('social.login', ['provider' => 'apple']) }}" class="account__social--link apple">Apple</a>
                        @endif
                    </div>
                @endif

                <!-- Register Now -->
                <p class="account__login--signup__text">{{ translate("Don't have an account?") }}
                    <a href="{{ route('user.registration') }}">Register now</a>
                </p>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    // Toggle between email and phone input
    function toggleEmailPhone(button) {
        const phoneGroup = $('.phone-form-group');
        const emailGroup = $('.email-form-group');
        const phoneOtpGroup = $('#phone-otp-input-group');

        if (phoneGroup.hasClass('d-none')) {
            // Switch to phone
            phoneGroup.removeClass('d-none');
            emailGroup.addClass('d-none');
            phoneOtpGroup.addClass('d-none');
            $(button).html('<i>*{{ translate("Use Email Instead") }}</i>');
            $('#login-btn').text('{{ translate("Send OTP") }}');
        } else {
            // Switch to email
            phoneGroup.addClass('d-none');
            emailGroup.removeClass('d-none');
            phoneOtpGroup.addClass('d-none');
            $(button).html('<i>*{{ translate("Use Phone Instead") }}</i>');
            updateLoginButtonText();
        }
    }

    // Update login button text based on current mode
    function updateLoginButtonText() {
        @if (get_setting('ask_email_otp_to_login'))
            $('#login-btn').text('{{ translate("Send OTP") }}');
        @else
            $('#login-btn').text('{{ translate("Login") }}');
        @endif
    }

    // Unified login handler
    function handleLogin() {
        const isPhoneVisible = !$('.phone-form-group').hasClass('d-none');
        const isEmailVisible = !$('.email-form-group').hasClass('d-none');

        if (isPhoneVisible) {
            handlePhoneLogin();
        } else if (isEmailVisible) {
            @if (get_setting('ask_email_otp_to_login'))
                handleEmailOtpLogin();
            @else
                handleRegularLogin();
            @endif
        } else {
            handleRegularLogin();
        }
    }

    // Handle phone OTP login
    function handlePhoneLogin() {
        const phone = $('#phone-code').val();
        const phoneOtp = $('#phone_otp').val();

        if (phoneOtp.trim() !== '') {
            // Submit form with phone OTP
            submitLoginForm();
        } else {
            // Send phone OTP
            if (!phone.trim()) {
                alert('{{ translate("Please enter phone number") }}');
                return;
            }

            $.ajax({
                url: "{{ route('user.login.send-otp') }}",
                type: "POST",
                data: {
                    _token: "{{ csrf_token() }}",
                    phone: phone,
                    country_code: $('input[name="country_code"]').val()
                },
                success: function(response) {
                    if (response.status === 'success') {
                        $('#phone-otp-input-group').removeClass('d-none');
                        $('#login-btn').text('{{ translate("Login") }}');
                        alert('{{ translate("OTP sent to your phone") }}');
                    } else {
                        alert(response.message);
                    }
                },
                error: function() {
                    alert('{{ translate("Error sending OTP. Please try again.") }}');
                }
            });
        }
    }

    // Handle email OTP login
    function handleEmailOtpLogin() {
        const email = $('input[name="email"]').val();
        const emailOtp = $('#email_otp').val();

        if (emailOtp.trim() !== '') {
            // Submit form with email OTP
            submitLoginForm();
        } else {
            // Send email OTP
            if (!email.trim()) {
                alert('{{ translate("Please enter email address") }}');
                return;
            }

            $.ajax({
                url: "{{ route('user.login.send-otp') }}",
                type: "POST",
                data: {
                    _token: "{{ csrf_token() }}",
                    email: email
                },
                success: function(response) {
                    if (response.status === 'success') {
                        $('#email-otp-input-group').removeClass('d-none');
                        $('#login-btn').text('{{ translate("Login") }}');
                        alert('{{ translate("OTP sent to your email") }}');
                    } else {
                        alert(response.message);
                    }
                },
                error: function() {
                    alert('{{ translate("Error sending OTP. Please try again.") }}');
                }
            });
        }
    }

    // Handle regular email/password login
    function handleRegularLogin() {
        const email = $('input[name="email"]').val();
        const password = $('#password').val();

        if (!email.trim()) {
            alert('{{ translate("Please enter email address") }}');
            return;
        }

        if (!password.trim()) {
            alert('{{ translate("Please enter password") }}');
            return;
        }

        submitLoginForm();
    }

    // Submit the login form
    function submitLoginForm() {
        const form = $('.form-default')[0];
        form.action = "{{ route('cart.login.submit') }}";
        form.submit();
    }

    // Password toggle functionality
    $('.password-toggle').click(function(){
        var $this = $(this);
        if ($this.siblings('input').attr('type') == 'password') {
            $this.siblings('input').attr('type', 'text');
            $this.removeClass('la-eye').addClass('la-eye-slash');
        } else {
            $this.siblings('input').attr('type', 'password');
            $this.removeClass('la-eye-slash').addClass('la-eye');
        }
    });
</script>
